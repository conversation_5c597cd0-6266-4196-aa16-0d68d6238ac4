#!/usr/bin/env python3
"""
Comprehensive debug script for Shopee API URLs.
Tests different endpoints to identify which ones work and which fail.
"""
import sys
import pathlib
import json
import time

# Add ShopeeAPI to path
shopee_api_path = str(pathlib.Path(__file__).parent / "ShopeeAPI")
if shopee_api_path not in sys.path:
    sys.path.append(shopee_api_path)

from ShopeeAPI.client import ShopeeAPI

def test_api_endpoint(session, url, payload, endpoint_name, description=""):
    """Test a single API endpoint and return results."""
    print(f"\n{'='*60}")
    print(f"Testing: {endpoint_name}")
    print(f"Description: {description}")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print(f"{'='*60}")
    
    try:
        response = session.post(
            url,
            params=session.get_common_params(),
            json=payload
        )
        
        result = {
            "endpoint": endpoint_name,
            "url": url,
            "status_code": response.status_code,
            "success": response.status_code == 200,
            "headers": dict(response.headers),
            "response_size": len(response.text) if response.text else 0
        }
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Size: {result['response_size']} bytes")
        print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                result["response_data"] = response_data
                result["has_error"] = bool(response_data.get('error'))
                result["response_keys"] = list(response_data.keys()) if isinstance(response_data, dict) else []
                
                print(f"✅ SUCCESS")
                print(f"Response Keys: {result['response_keys']}")
                print(f"Has Error: {result['has_error']}")
                
                if response_data.get('error'):
                    print(f"API Error: {response_data['error']}")
                elif 'data' in response_data:
                    data = response_data['data']
                    if isinstance(data, dict):
                        print(f"Data Keys: {list(data.keys())}")
                        # Check for specific order fields
                        order_fields = ['order_sn', 'order_id', 'total_price', 'buyer_user', 'order_items']
                        found_fields = [field for field in order_fields if field in data]
                        if found_fields:
                            print(f"Order Fields Found: {found_fields}")
                    elif isinstance(data, list):
                        print(f"Data is a list with {len(data)} items")
                        if data and isinstance(data[0], dict):
                            print(f"First item keys: {list(data[0].keys())}")
                
            except json.JSONDecodeError:
                result["json_error"] = True
                print(f"⚠️  Response is not valid JSON")
                print(f"Response preview: {response.text[:200]}...")
                
        else:
            result["error_response"] = response.text[:500]
            print(f"❌ FAILED")
            print(f"Error Response: {response.text[:200]}...")
            
        return result
        
    except Exception as e:
        result = {
            "endpoint": endpoint_name,
            "url": url,
            "error": str(e),
            "success": False
        }
        print(f"❌ EXCEPTION: {e}")
        return result

def main():
    """Main debug function."""
    print("🔍 Shopee API Debug Script")
    print("=" * 60)
    
    try:
        # Initialize API client
        api = ShopeeAPI()
        order_service = api.order_service
        session = order_service.session
        config = order_service.config
        
        print(f"Shop ID: {config.shop_id}")
        print(f"Region ID: {config.region_id}")

        # Check authorization from session headers
        auth_header = session.session.headers.get('Authorization', 'Not found')
        print(f"Authorization: {auth_header[:50] if auth_header != 'Not found' else auth_header}...")
        
        # Test order number
        test_order_sn = "250604MPB2VBWR"  # From your example
        if len(sys.argv) > 1:
            test_order_sn = sys.argv[1]
        
        print(f"Test Order SN: {test_order_sn}")
        
        results = []
        
        # Test 1: Initial Order List (Search) - This should work if auth is good
        print("\n" + "🔍 Testing order search functionality...")
        search_payload = {
            "order_list_tab": 100,  # All orders
            "search_keyword": test_order_sn,
            "page_number": 1,
            "page_size": 5
        }
        
        result1 = test_api_endpoint(
            session,
            config.urls["initial_order_list"],
            search_payload,
            "initial_order_list",
            "Search for orders - basic auth test"
        )
        results.append(result1)
        
        # Test 2: Order Details (Batch) - Test the batch endpoint
        print("\n" + "📋 Testing batch order details...")
        batch_payload = {
            "order_list_tab": 100,
            "order_param_list": [
                {
                    "order_id": 202711189213112,  # From your example
                    "shop_id": config.shop_id,
                    "region_id": config.region_id
                }
            ]
        }
        
        result2 = test_api_endpoint(
            session,
            config.urls["order_details"],
            batch_payload,
            "order_details",
            "Batch order details endpoint"
        )
        results.append(result2)
        
        # Test 3: Get One Order with order_sn - The problematic endpoint
        print("\n" + "🎯 Testing get_one_order with order_sn...")
        sn_payload = {
            "order_sn": test_order_sn,
            "shop_id": config.shop_id,
            "region_id": config.region_id
        }
        
        result3 = test_api_endpoint(
            session,
            config.urls["order_details_specific"],
            sn_payload,
            "get_one_order_sn",
            "Get single order by order_sn - PROBLEMATIC"
        )
        results.append(result3)
        
        # Test 4: Get One Order with order_id - Try with order_id instead
        print("\n" + "🎯 Testing get_one_order with order_id...")
        id_payload = {
            "order_id": 202711189213112,  # From your example
            "shop_id": config.shop_id,
            "region_id": config.region_id
        }
        
        result4 = test_api_endpoint(
            session,
            config.urls["order_details_specific"],
            id_payload,
            "get_one_order_id",
            "Get single order by order_id"
        )
        results.append(result4)
        
        # Test 5: Alternative endpoint if available
        if "order_details_alternative" in config.urls:
            print("\n" + "🔄 Testing alternative order details endpoint...")
            result5 = test_api_endpoint(
                session,
                config.urls["order_details_alternative"],
                sn_payload,
                "order_details_alternative",
                "Alternative order details endpoint"
            )
            results.append(result5)
        
        # Test 6: Try different payload combinations
        print("\n" + "🧪 Testing combined payload...")
        combined_payload = {
            "order_sn": test_order_sn,
            "order_id": 202711189213112,
            "shop_id": config.shop_id,
            "region_id": config.region_id
        }
        
        result6 = test_api_endpoint(
            session,
            config.urls["order_details_specific"],
            combined_payload,
            "get_one_order_combined",
            "Get single order with both order_sn and order_id"
        )
        results.append(result6)
        
        # Summary
        print("\n" + "📊 SUMMARY")
        print("=" * 60)
        
        working_endpoints = [r for r in results if r.get('success')]
        failing_endpoints = [r for r in results if not r.get('success')]
        
        print(f"✅ Working endpoints: {len(working_endpoints)}")
        for r in working_endpoints:
            print(f"   - {r['endpoint']}: {r['status_code']}")
        
        print(f"❌ Failing endpoints: {len(failing_endpoints)}")
        for r in failing_endpoints:
            status = r.get('status_code', 'Exception')
            print(f"   - {r['endpoint']}: {status}")
        
        # Analysis
        print("\n" + "🔍 ANALYSIS")
        print("=" * 60)
        
        if len(working_endpoints) > 0:
            print("✅ Authentication appears to be working (some endpoints succeed)")
        else:
            print("❌ Authentication may be failing (all endpoints fail)")
        
        # Check specifically for get_one_order issues
        get_one_results = [r for r in results if 'get_one_order' in r['endpoint']]
        if get_one_results:
            all_get_one_fail = all(not r.get('success') for r in get_one_results)
            if all_get_one_fail:
                print("🎯 ISSUE IDENTIFIED: get_one_order endpoint specifically failing")
                print("   Possible causes:")
                print("   - Endpoint requires different authentication")
                print("   - Payload format is incorrect")
                print("   - Order doesn't exist or access denied")
                print("   - API endpoint has changed")
        
        # Save detailed results
        with open("shopee_api_debug_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\n📄 Detailed results saved to: shopee_api_debug_results.json")
        
    except Exception as e:
        print(f"❌ Script Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
