"""
Services for managing Shopee orders.
"""
from typing import Dict, Any, List, Tuple, Optional
from urllib.parse import quote
import requests
import time

from ..core.session import ShopeeSession
from ..core.config import ShopeeConfig
from ..core.exceptions import RequestError, ResourceNotFoundError


class OrderService:
    """
    Service for interacting with Shopee order APIs.
    """

    def __init__(self, session: ShopeeSession, config: ShopeeConfig):
        """
        Initialize with session and config.

        Args:
            session: ShopeeSession instance
            config: ShopeeConfig instance
        """
        self.session = session
        self.config = config

    def get_initial_order_list(self, order_status: str, order_sn: Optional[str] = None) -> Dict[str, Any]:
        """
        Get initial order list based on status.

        Args:
            order_status: Order status filter (to_ship, shipped, completed, all)
            order_sn: Optional order number to search for

        Returns:
            API response data

        Raises:
            ValueError: If order_status is invalid
        """
        base_filter = {
            "fulfillment_type": 0,
            "is_drop_off": 0,
            "fulfillment_source": 0,
            "prescription_filter": 0
        }

        if order_status == "to_ship":
            order_list_tab = self.config.order_tabs["to_ship"]
            filter_params = {
                **base_filter,
                "status_list": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 883, 884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 913, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 954, 955, 956, 957, 958, 959, 960, 961, 962, 963, 964, 965, 966, 967, 968, 969, 970, 971, 972, 973, 974, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000]
            }
            sort_params = {"create_time": "desc"}
        elif order_status == "shipped":
            order_list_tab = self.config.order_tabs["shipped"]
            filter_params = {**base_filter}
            sort_params = {"create_time": "desc"}
        elif order_status == "completed":
            order_list_tab = self.config.order_tabs["completed"]
            filter_params = {**base_filter}
            sort_params = {"create_time": "desc"}
        elif order_status == "all":
            order_list_tab = self.config.order_tabs["all"]
            filter_params = {**base_filter}
            sort_params = {"create_time": "desc"}
        else:
            raise ValueError(f"Invalid order status: {order_status}")

        initial_payload = {
            "order_list_tab": order_list_tab,
            "entity_type": 1,
            "pagination": {"from_page_number": 1, "page_number": 1, "page_size": self.config.page_size},
            "filter": filter_params,
            "sort": sort_params
        }

        if order_sn:
            initial_payload["search_param"] = {"keyword": order_sn, "category": 1}

        try:
            initial_response = self.session.post(
                self.config.urls["initial_order_list"],
                params=self.session.get_common_params(),
                json=initial_payload
            )

            # Check for 403 Forbidden specifically
            if initial_response.status_code == 403:
                from ..core.exceptions import AuthenticationError
                print(f"403 Forbidden: Access denied to {self.config.urls['initial_order_list']}")
                print("This is likely due to invalid or expired credentials.")
                print("Please update your authorization_code and cookie in config.json")
                raise AuthenticationError("403 Forbidden: Invalid or expired credentials. Please update your credentials.")

            # Check for other errors
            initial_response.raise_for_status()
            return initial_response.json()
        except Exception as e:
            # Re-raise authentication errors as is
            if hasattr(e, '__class__') and e.__class__.__name__ == 'AuthenticationError':
                raise e
            # Wrap other errors
            raise RequestError(f"Error fetching initial order list: {str(e)}")

    def get_order_details(self, order_ids: List[int], order_status: str) -> Dict[str, Any]:
        """
        Get detailed order information.

        Args:
            order_ids: List of order IDs to fetch
            order_status: Order status filter

        Returns:
            API response data with order details

        Raises:
            ValueError: If order_status is invalid
        """
        # Set a small batch size to avoid response size issues
        BATCH_SIZE = 1

        if order_status == "to_ship":
            order_list_tab = self.config.order_tabs["to_ship"]
        elif order_status == "shipped":
            order_list_tab = self.config.order_tabs["shipped"]
        elif order_status == "completed":
            order_list_tab = self.config.order_tabs["completed"]
        elif order_status == "all":
            order_list_tab = self.config.order_tabs["all"]
        else:
            raise ValueError(f"Invalid order status: {order_status}")

        all_results = []
        for i in range(0, len(order_ids), BATCH_SIZE):
            batch_order_ids = order_ids[i:i + BATCH_SIZE]

            details_payload = {
                "order_list_tab": order_list_tab,
                "order_param_list": [
                    {"order_id": order_id, "shop_id": self.config.shop_id, "region_id": self.config.region_id}
                    for order_id in batch_order_ids
                ]
            }

            try:
                details_response = self.session.post(
                    self.config.urls["order_details"],
                    params=self.session.get_common_params(),
                    json=details_payload
                )

                # Check for 403 Forbidden specifically
                if details_response.status_code == 403:
                    from ..core.exceptions import AuthenticationError
                    print(f"403 Forbidden: Access denied to {self.config.urls['order_details']}")
                    print("This is likely due to invalid or expired credentials.")
                    print("Please update your authorization_code and cookie in config.json")
                    raise AuthenticationError("403 Forbidden: Invalid or expired credentials. Please update your credentials.")

                details_response.raise_for_status()
                response_data = details_response.json()

                if 'data' in response_data and 'card_list' in response_data['data']:
                    all_results.extend(response_data['data']['card_list'])

                # Add a small delay between requests to avoid rate limiting
                time.sleep(0.5)
            except Exception as e:
                # Re-raise authentication errors as is
                if hasattr(e, '__class__') and e.__class__.__name__ == 'AuthenticationError':
                    raise e
                # Wrap other errors
                raise RequestError(f"Error fetching order details: {str(e)}")

        return {"data": {"card_list": all_results}}

    def process_orders(self, initial_data: Dict[str, Any], order_status: str, status_filter: Optional[str] = None) -> Dict[str, Any]:
        """
        Process and filter orders from initial data.

        Args:
            initial_data: Data from get_initial_order_list
            order_status: Order status filter
            status_filter: Additional status filter

        Returns:
            Processed and filtered order data
        """
        if not isinstance(initial_data, dict) or 'error' in initial_data:
            raise RequestError(f"Error in initial data: {initial_data}")

        if 'data' not in initial_data or not initial_data.get('data', {}).get('index_list'):
            return {"data": {"card_list": []}}

        order_ids = [order['order_id'] for order in initial_data['data']['index_list']]
        details_data = self.get_order_details(order_ids, order_status)

        if not details_data.get('data', {}).get('card_list'):
            return {"data": {"card_list": []}}

        if status_filter and order_status != "all":
            filtered_orders = [
                order for order in details_data['data']['card_list']
                if (order_status == "to_ship" and 'package_card' in order and
                    order['package_card']['status_info']['status'] == status_filter) or
                   (order_status != "to_ship" and 'order_card' in order and
                    order['order_card']['status_info']['status'] == status_filter)
            ]
            return {"data": {"card_list": filtered_orders}}

        return details_data

    def get_to_ship_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'To Ship' status.

        Returns:
            Orders with To Ship status
        """
        initial_data = self.get_initial_order_list("to_ship")
        return self.process_orders(initial_data, "to_ship")

    def get_shipped_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'Shipped' status.

        Returns:
            Orders with Shipped status
        """
        initial_data = self.get_initial_order_list("shipped")
        return self.process_orders(initial_data, "shipped", status_filter="Shipped")

    def get_completed_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'Completed' status.

        Returns:
            Orders with Completed status
        """
        initial_data = self.get_initial_order_list("completed")
        return self.process_orders(initial_data, "completed", status_filter="Completed")

    def search_order(self, order_sn: str) -> Dict[str, Any]:
        """
        Search for an order by order number.

        Args:
            order_sn: Order number to search for

        Returns:
            Order data if found
        """
        initial_data = self.get_initial_order_list("all", order_sn)
        return self.process_orders(initial_data, "all")

    def get_order_status(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get status of an order.

        Args:
            order_sn: Order number

        Returns:
            Tuple with order status data and HTTP status code
        """
        initial_data = self.get_initial_order_list("all", order_sn)
        order_data = self.process_orders(initial_data, "all")

        if not order_data['data']['card_list']:
            raise ResourceNotFoundError(f"Order not found: {order_sn}")

        order = order_data['data']['card_list'][0]

        # Handle "To Ship" status
        if 'package_level_order_card' in order:
            package = order['package_level_order_card']['package_list'][0]
            status = package['status_info']['status']
            order_sn = order['package_level_order_card']['card_header']['order_sn']
        # Handle "Completed" or "Shipped" status
        elif 'order_card' in order:
            if('return_id' in order['order_card']['order_ext_info']):
                status = "Refunded"
            else:
                status = order['order_card']['status_info']['status']
        else:
            raise RequestError("Unexpected order structure")

        return {
            "order_sn": order_sn,
            "status": status
        }, 200

    def get_buyer_orders(self, buyer_id: int, order_type: str = "all", page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer.

        Args:
            buyer_id: Buyer ID
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page

        Returns:
            Tuple with buyer orders data and HTTP status code
        """
        # Validate order_type
        valid_order_types = ["all", "to_ship", "unpaid", "shipping", "completed", "cancelled", "refund"]
        if order_type not in valid_order_types:
            return {"error": f"Invalid order type: {order_type}. Valid types are: {', '.join(valid_order_types)}"}, 400

        params = {
            "shop_id": self.config.shop_id,
            "buyer_id": buyer_id,
            "type": order_type,
            "per_page": per_page,
            "page": page
        }

        try:
            response = self.session.get(
                self.config.urls["buyer_orders"],
                params=params
            )

            if response.status_code != 200:
                return {"error": f"Failed to get buyer orders: {response.text}"}, response.status_code

            response_data = response.json()
            if response_data.get('error'):
                return {"error": f"Failed to get buyer orders: {response_data['error']}"}, 400

            return response_data, 200
        except Exception as e:
            return {"error": f"Failed to get buyer orders: {str(e)}"}, 500

    def get_buyer_orders_by_username(self, username: str, order_type: str = "all", page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer by username.

        Args:
            username: Buyer username
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page

        Returns:
            Tuple with buyer orders data and HTTP status code
        """
        # Validate order_type
        valid_order_types = ["all", "to_ship", "unpaid", "shipping", "completed", "cancelled", "refund"]
        if order_type not in valid_order_types:
            return {"error": f"Invalid order type: {order_type}. Valid types are: {', '.join(valid_order_types)}"}, 400
        # Import here to avoid circular imports
        from ..services.chat import ChatService
        chat_service = ChatService(self.session, self.config)

        try:
            # First, get the conversation info to find the buyer_id
            conversation_info, error = chat_service.get_conversation_info_by_username(username)

            if error:
                return {"error": f"Failed to find user '{username}': {error}"}, 404

            # Extract buyer_id from conversation info
            buyer_id = None

            # Try different paths in the response structure
            if 'to_id' in conversation_info:
                buyer_id = conversation_info.get('to_id')
            elif 'data' in conversation_info and 'conversation_info' in conversation_info['data']:
                buyer_id = conversation_info['data']['conversation_info'].get('to_id')
            elif 'id' in conversation_info and 'to_id' in conversation_info:
                buyer_id = conversation_info.get('to_id')

            if not buyer_id:
                return {"error": f"Could not extract buyer ID for username '{username}'"}, 404

            print(f"Found buyer ID {buyer_id} for username {username}")

            # Now get the buyer orders using the buyer_id
            return self.get_buyer_orders(buyer_id, order_type, page, per_page)
        except Exception as e:
            return {"error": f"Failed to get buyer orders by username: {str(e)}"}, 500

    def get_order_details_by_sn(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get detailed information for a specific order by order number.

        Args:
            order_sn: Order number

        Returns:
            Tuple with order details and HTTP status code
        """
        try:
            return self._get_order_details_direct(order_sn)
        except Exception as e:
            return {"error": f"Failed to get order details: {str(e)}"}, 500

    def get_order_details_by_id(self, order_id: int) -> Tuple[Dict[str, Any], int]:
        """
        Get detailed information for a specific order by order ID.

        Args:
            order_id: Order ID

        Returns:
            Tuple with order details and HTTP status code
        """
        try:
            return self._get_order_details_direct_by_id(order_id)
        except Exception as e:
            return {"error": f"Failed to get order details: {str(e)}"}, 500

    def _get_order_details_direct_by_id(self, order_id: int) -> Tuple[Dict[str, Any], int]:
        """
        Get order details directly using order_id with GET request.

        Args:
            order_id: Order ID

        Returns:
            Tuple with order details and HTTP status code
        """
        # Use the correct GET method with query parameters (like browser)
        query_params = self.session.get_common_params()
        query_params['order_id'] = str(order_id)

        response = self.session.get(
            self.config.urls["order_details_specific"],
            params=query_params
        )

        if response.status_code == 200:
            response_data = response.json()
            if not response_data.get('error'):
                return response_data, 200
            else:
                return {"error": f"API error: {response_data.get('error')}"}, 400
        else:
            return {"error": f"HTTP {response.status_code}: {response.text[:200]}"}, response.status_code

    def _get_order_details_direct(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get order details by first searching for order_id, then using direct GET.

        Args:
            order_sn: Order number

        Returns:
            Tuple with order details and HTTP status code
        """
        # Try to search for the order to get the order ID
        # But use a more robust search approach
        try:

            # Try the batch endpoint to search for the order
            # This is more likely to work than the search endpoint
            search_payload = {
                "order_list_tab": self.config.order_tabs["all"],
                "search_keyword": order_sn,
                "page_number": 1,
                "page_size": 5
            }

            # Use POST for search (this is what works)
            search_response = self.session.post(
                self.config.urls["initial_order_list"],
                params=self.session.get_common_params(),
                json=search_payload
            )

            if search_response.status_code != 200:
                # If search fails, try some common order IDs or return error
                return {"error": f"Could not search for order {order_sn}: HTTP {search_response.status_code}"}, search_response.status_code

            search_data = search_response.json()
            if search_data.get('error'):
                return {"error": f"Search API error: {search_data['error']}"}, 400

            # Process the search results
            order_data = self.process_orders(search_data, "all")

            if not order_data['data']['card_list']:
                return {"error": f"Order not found: {order_sn}"}, 404

            order = order_data['data']['card_list'][0]

            # Extract order ID
            order_id = None
            if 'package_level_order_card' in order:
                order_id = order['package_level_order_card']['order_ext_info']['order_id']
            elif 'order_card' in order:
                order_id = order['order_card']['order_ext_info']['order_id']
            else:
                return {"error": "Unexpected order structure"}, 500

            # Now get the detailed order info using the order_id
            return self._get_order_details_direct_by_id(order_id)

        except Exception as e:
            # If search completely fails, try some fallback approaches
            return {"error": f"Failed to search for order: {str(e)}"}, 500

    def ship_order(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Ship an order.

        Args:
            order_sn: Order number

        Returns:
            Tuple with response data and HTTP status code
        """
        # First, get the order details to extract the order ID
        initial_data = self.get_initial_order_list("all", order_sn)
        order_data = self.process_orders(initial_data, "all")

        if not order_data['data']['card_list']:
            raise ResourceNotFoundError(f"Order not found: {order_sn}")

        order = order_data['data']['card_list'][0]

        # Extract order ID
        if 'package_level_order_card' in order:
            order_id = order['package_level_order_card']['order_ext_info']['order_id']
        elif 'order_card' in order:
            order_id = order['order_card']['order_ext_info']['order_id']
        else:
            raise RequestError("Unexpected order structure")

        # Prepare shipping payload
        payload = {
            "order_id": order_id,
            "pickup_method": 0,
            "pickup_time_id": 0,
            "dropoff": {
                "branch_id": 0,
                "sender_real_name": "",
                "tracking_no": ""
            }
        }

        try:
            response = self.session.post(
                self.config.urls["init_order"],
                params=self.session.get_common_params(),
                json=payload
            )

            if response.status_code != 200:
                return {"error": f"Failed to ship order: {response.text}"}, response.status_code

            response_data = response.json()
            if response_data.get('error'):
                return {"error": f"Failed to ship order: {response_data['error']}"}, 400

            return {"message": "Order shipped successfully", "data": response_data}, 200
        except Exception as e:
            return {"error": f"Failed to ship order: {str(e)}"}, 500
