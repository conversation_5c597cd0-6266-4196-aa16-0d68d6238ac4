"""
Main client for Shopee API.
"""
from typing import Dict, Any, Optional, Tu<PERSON>, List
import os

from .core.auth import CredentialManager
from .core.session import ShopeeSession
from .core.config import ShopeeConfig
from .services.orders import OrderService
from .services.chat import ChatService
from .services.websocket import WebSocketService


class ShopeeAPI:
    """
    Main client for interacting with Shopee API.
    """

    def __init__(
        self,
        authorization_code: str = '',
        cookie: str = '',
        config_path: Optional[str] = None,
        shop_id: Optional[int] = None,
        region_id: Optional[str] = None,
        timeout: int = 60,
        requests_per_minute: int = 30
    ):
        """
        Initialize the Shopee API client.

        Args:
            authorization_code: Bearer token for authorization
            cookie: Cookie string containing authentication tokens
            config_path: Path to config file
            shop_id: Shop ID (overrides config file)
            region_id: Region ID (overrides config file)
            timeout: Request timeout in seconds
            requests_per_minute: Maximum requests per minute
        """
        # If config_path is not provided, use the default path in the ShopeeAPI directory
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
            print(f"Using default config path: {config_path}")

        # Initialize config first to load credentials from file
        self.config = ShopeeConfig(config_path)

        # Load credentials from config file if not provided as parameters
        if not authorization_code or not cookie:
            config_credentials = self._load_credentials_from_config(config_path)
            if not authorization_code:
                authorization_code = config_credentials.get('authorization_code', '')
            if not cookie:
                cookie = config_credentials.get('cookie', '')

        # Initialize credential manager with loaded credentials
        self.credential_manager = CredentialManager(authorization_code, cookie)

        # Override config if parameters are provided
        if shop_id is not None:
            self.config.shop_id = shop_id
        if region_id is not None:
            self.config.region_id = region_id
        if timeout is not None:
            self.config.timeout = timeout

        # Initialize session
        self.session = ShopeeSession(
            self.credential_manager,
            timeout=self.config.timeout,
            requests_per_minute=requests_per_minute
        )

        # Initialize services
        self.order_service = OrderService(self.session, self.config)
        self.chat_service = ChatService(self.session, self.config)
        self.websocket_service = WebSocketService(self.session, self.config, self.chat_service)

    def _load_credentials_from_config(self, config_path: str) -> Dict[str, str]:
        """
        Load credentials from config file.

        Args:
            config_path: Path to config file

        Returns:
            Dictionary containing authorization_code and cookie
        """
        try:
            import json
            with open(config_path, 'r') as f:
                config_data = json.load(f)

            authorization_code = config_data.get('AUTHORIZATION_CODE', '')
            cookie = config_data.get('COOKIE', '')

            # Also check for COOKIE_JSON and convert to string if needed
            if not cookie and 'COOKIE_JSON' in config_data:
                cookie_json = config_data['COOKIE_JSON']
                if isinstance(cookie_json, list):
                    # Convert JSON array to cookie string
                    cookie_parts = []
                    for cookie_item in cookie_json:
                        if 'name' in cookie_item and 'value' in cookie_item:
                            cookie_parts.append(f"{cookie_item['name']}={cookie_item['value']}")
                    cookie = '; '.join(cookie_parts)

            print(f"Loaded credentials from config: Auth={authorization_code[:30]}..., Cookie length={len(cookie)}")

            return {
                'authorization_code': authorization_code,
                'cookie': cookie
            }
        except Exception as e:
            print(f"Error loading credentials from config: {e}")
            return {
                'authorization_code': '',
                'cookie': ''
            }

    def update_credentials(self, authorization_code: str, cookie: str) -> bool:
        """
        Update API credentials.

        Args:
            authorization_code: Bearer token for authorization
            cookie: Cookie string containing authentication tokens

        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            self.credential_manager.update_credentials(authorization_code, cookie)
            # Refresh session with new credentials
            self.session.refresh_credentials()

            # Validate credentials
            return self.credential_manager.validate_credentials()
        except Exception as e:
            print(f"Error updating credentials: {str(e)}")
            return False

    def check_credentials(self) -> bool:
        """
        Check if current credentials are valid.

        Returns:
            True if credentials are valid, False otherwise
        """
        try:
            return self.credential_manager.validate_credentials()
        except Exception as e:
            print(f"Error checking credentials: {str(e)}")
            return False

    def adapt_to_api_changes(self) -> None:
        """
        Adapt to changes in Shopee's API requirements.

        This method should be called when Shopee changes their API requirements,
        such as removing the need for certain cookies or parameters.
        """
        print("Adapting to Shopee API changes...")

        # Shopee has changed their authentication mechanism
        # They no longer require SPC_CDS or SPC_CDS_VER parameters
        # Authentication is now handled entirely through the Authorization header and Cookie

        # Check if we need to update the session headers
        if not self.session.session.headers.get("Authorization") or not self.session.session.headers.get("Cookie"):
            print("Updating session headers with current credentials...")
            self.session.refresh_credentials()

    def save_config(self, config_path: str) -> None:
        """
        Save current configuration to file.

        Args:
            config_path: Path to save the config file
        """
        self.config.save_to_file(config_path)

    # Order-related methods
    def get_to_ship_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'To Ship' status.

        Returns:
            Orders with To Ship status
        """
        return self.order_service.get_to_ship_orders()

    def get_shipped_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'Shipped' status.

        Returns:
            Orders with Shipped status
        """
        return self.order_service.get_shipped_orders()

    def get_completed_orders(self) -> Dict[str, Any]:
        """
        Get orders with 'Completed' status.

        Returns:
            Orders with Completed status
        """
        return self.order_service.get_completed_orders()

    def search_order(self, order_sn: str) -> Dict[str, Any]:
        """
        Search for an order by order number.

        Args:
            order_sn: Order number to search for

        Returns:
            Order data if found
        """
        return self.order_service.search_order(order_sn)

    def get_order_status(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get status of an order.

        Args:
            order_sn: Order number

        Returns:
            Tuple with order status data and HTTP status code
        """
        return self.order_service.get_order_status(order_sn)

    def get_order_details(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Get detailed information for a specific order.

        Args:
            order_sn: Order number

        Returns:
            Tuple with order details and HTTP status code
        """
        return self.order_service.get_order_details_by_sn(order_sn)

    def get_buyer_orders(self, buyer_id: int, order_type: str = "all", page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer.

        Args:
            buyer_id: Buyer ID
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page

        Returns:
            Tuple with buyer orders data and HTTP status code
        """
        return self.order_service.get_buyer_orders(buyer_id, order_type, page, per_page)

    def get_buyer_orders_by_username(self, username: str, order_type: str = "all", page: int = 1, per_page: int = 20) -> Tuple[Dict[str, Any], int]:
        """
        Get orders from a specific buyer by username.

        Args:
            username: Buyer username
            order_type: Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)
            page: Page number
            per_page: Number of orders per page

        Returns:
            Tuple with buyer orders data and HTTP status code
        """
        return self.order_service.get_buyer_orders_by_username(username, order_type, page, per_page)

    def ship_order(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Ship an order.

        Args:
            order_sn: Order number to ship

        Returns:
            Tuple with shipping result and HTTP status code
        """
        return self.order_service.ship_order(order_sn)

    # Chat-related methods
    def send_chat_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send a text message to a user.

        Args:
            payload: Dictionary containing message details
                - text: Message text
                - username: Username to send to
                - force_send_cancel_order_warning: Whether to force send despite cancel warning
                - comply_cancel_order_warning: Whether to comply with cancel warning

        Returns:
            Tuple with response data and HTTP status code
        """
        return self.chat_service.send_chat_message(payload)

    def send_image_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send an image message to a user.

        Args:
            payload: Dictionary containing message details
                - username: Username to send to
                - image_url: URL of the image to send

        Returns:
            Tuple with response data and HTTP status code
        """
        return self.chat_service.send_image_message(payload)

    def send_order_message(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Send an order message to a user.

        Args:
            order_sn: Order number

        Returns:
            Tuple with response data and HTTP status code
        """
        return self.chat_service.send_order_message(order_sn)

    def get_conversation_info_by_username(self, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by username.

        Args:
            username: Username to get conversation info for

        Returns:
            Tuple with conversation info and error (if any)
        """
        return self.chat_service.get_conversation_info_by_username(username)

    def get_conversation_info_by_ordersn(self, order_sn: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by order number.

        Args:
            order_sn: Order number to get conversation info for

        Returns:
            Tuple with conversation info and error (if any)
        """
        return self.chat_service.get_conversation_info_by_ordersn(order_sn)

    def get_recent_conversations(self, unread_only: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Get recent conversations.

        Args:
            unread_only: Whether to only retrieve unread conversations

        Returns:
            Tuple with recent conversations data and HTTP status code
        """
        return self.chat_service.get_recent_conversations(unread_only)

    def get_recent_latest_messages(self) -> List[Dict[str, Any]]:
        """
        Get recent latest messages from conversations.

        Returns:
            List of recent messages
        """
        return self.chat_service.get_recent_latest_messages()

    def get_conversation_messages(self, conversation_id: str, offset: int = 0, limit: int = 20, direction: str = "older") -> Tuple[Dict[str, Any], int]:
        """
        Get messages from a specific conversation.

        Args:
            conversation_id: Conversation ID
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages ('older' or 'newer')

        Returns:
            Tuple with conversation messages and HTTP status code
        """
        return self.chat_service.get_conversation_messages(conversation_id, offset, limit, direction)

    def get_conversation_messages_by_username(self, username: str, offset: int = 0, limit: int = 20, direction: str = "older", force_refresh: bool = False) -> Tuple[Dict[str, Any], int, bool]:
        """
        Get messages from a conversation with a specific user by username.

        This method first finds the conversation ID for the given username,
        then retrieves the messages for that conversation.

        Args:
            username: Username to get conversation messages for
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages (only 'older' is supported)
            force_refresh: Force refresh from Shopee API instead of using cache

        Returns:
            Tuple with conversation messages, HTTP status code, and a boolean indicating if the response was cached
        """
        return self.chat_service.get_conversation_messages_by_username(username, offset, limit, direction, force_refresh)

    def set_conversation_unread(self, conversation_id: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread.

        Args:
            conversation_id: Conversation ID to mark as unread

        Returns:
            Tuple with response data and HTTP status code
        """
        return self.chat_service.set_conversation_unread(conversation_id)

    def set_conversation_unread_by_username(self, username: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread by username.

        Args:
            username: Username of the conversation to mark as unread

        Returns:
            Tuple with response data and HTTP status code
        """
        return self.chat_service.set_conversation_unread_by_username(username)

    def clear_cache(self) -> Dict[str, Any]:
        """
        Clear all caches.

        Returns:
            Dictionary with status information
        """
        return self.chat_service.clear_cache()

    async def get_chat_login_info(self) -> Tuple[Dict[str, Any], int]:
        """
        Get chat login information from Shopee API.

        This method calls the mini/login endpoint to get the chat token and other login information.

        Returns:
            Tuple with login info data and HTTP status code
        """
        return await self.websocket_service.get_chat_login_info()