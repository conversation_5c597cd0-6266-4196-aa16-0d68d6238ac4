#!/usr/bin/env python3
"""
Simple authentication test for Shopee API.
Tests if credentials are working by trying different endpoints.
"""
import sys
import pathlib
import json
import requests
from datetime import datetime

# Add ShopeeAPI to path
shopee_api_path = str(pathlib.Path(__file__).parent / "ShopeeAPI")
if shopee_api_path not in sys.path:
    sys.path.append(shopee_api_path)

def load_config():
    """Load config from ShopeeAPI/config.json"""
    config_path = pathlib.Path(__file__).parent / "ShopeeAPI" / "config.json"
    with open(config_path, 'r') as f:
        return json.load(f)

def test_raw_request(url, headers, payload):
    """Test a raw HTTP request without ShopeeAPI wrapper."""
    print(f"\n🔍 Testing: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ SUCCESS - Response keys: {list(data.keys())}")
                return True, data
            except:
                print(f"✅ SUCCESS - Non-JSON response: {response.text[:100]}...")
                return True, response.text
        else:
            print(f"❌ FAILED - Response: {response.text[:200]}...")
            return False, response.text
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return False, str(e)

def main():
    """Main test function."""
    print("🔐 Simple Shopee Authentication Test")
    print("=" * 50)
    
    try:
        # Load config
        config = load_config()
        
        # Extract credentials
        auth_code = config.get('AUTHORIZATION_CODE', '')
        cookie = config.get('COOKIE', '')
        shop_id = config.get('SHOP_ID')
        region_id = config.get('REGION_ID', 'MY')
        
        print(f"Shop ID: {shop_id}")
        print(f"Region ID: {region_id}")
        print(f"Auth Code: {auth_code[:50]}..." if auth_code else "❌ No auth code")
        print(f"Cookie Length: {len(cookie)} chars" if cookie else "❌ No cookie")
        
        if not auth_code or not cookie:
            print("\n❌ Missing credentials in config.json!")
            return
        
        # Prepare headers
        headers = {
            'Authorization': auth_code,
            'Cookie': cookie,
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://seller.shopee.com.my/',
            'Origin': 'https://seller.shopee.com.my'
        }
        
        # Test 1: Simple order search (most basic test)
        print(f"\n{'='*50}")
        print("TEST 1: Order Search (Basic Auth Test)")
        print(f"{'='*50}")
        
        search_payload = {
            "order_list_tab": 100,
            "search_keyword": "",
            "page_number": 1,
            "page_size": 1
        }
        
        success1, result1 = test_raw_request(
            "https://seller.shopee.com.my/api/v3/order/search_order_list_index",
            headers,
            search_payload
        )
        
        # Test 2: Get one order (the problematic endpoint)
        print(f"\n{'='*50}")
        print("TEST 2: Get One Order (Problematic Endpoint)")
        print(f"{'='*50}")
        
        order_payload = {
            "order_sn": "250604MPB2VBWR",
            "shop_id": shop_id,
            "region_id": region_id
        }
        
        success2, result2 = test_raw_request(
            "https://seller.shopee.com.my/api/v3/order/get_one_order",
            headers,
            order_payload
        )
        
        # Test 3: Try with different common params
        print(f"\n{'='*50}")
        print("TEST 3: Order Search with Common Params")
        print(f"{'='*50}")
        
        # Add common query parameters that Shopee usually expects
        common_params = {
            'SPC_CDS': '1',
            'SPC_CDS_VER': '2'
        }
        
        url_with_params = "https://seller.shopee.com.my/api/v3/order/search_order_list_index"
        param_string = "&".join([f"{k}={v}" for k, v in common_params.items()])
        full_url = f"{url_with_params}?{param_string}"
        
        success3, result3 = test_raw_request(
            full_url,
            headers,
            search_payload
        )
        
        # Test 4: Check cookie expiration
        print(f"\n{'='*50}")
        print("TEST 4: Cookie Expiration Check")
        print(f"{'='*50}")
        
        cookie_json = config.get('COOKIE_JSON', [])
        if cookie_json:
            print("Checking cookie expiration dates:")
            current_time = datetime.now().timestamp()
            
            for cookie_item in cookie_json:
                if 'expirationDate' in cookie_item:
                    exp_date = cookie_item['expirationDate']
                    cookie_name = cookie_item.get('name', 'Unknown')
                    
                    if exp_date < current_time:
                        print(f"❌ EXPIRED: {cookie_name} (expired {datetime.fromtimestamp(exp_date)})")
                    else:
                        days_left = (exp_date - current_time) / 86400
                        print(f"✅ VALID: {cookie_name} (expires in {days_left:.1f} days)")
        else:
            print("No COOKIE_JSON found in config")
        
        # Summary
        print(f"\n{'='*50}")
        print("SUMMARY")
        print(f"{'='*50}")
        
        tests = [
            ("Order Search", success1),
            ("Get One Order", success2),
            ("Search with Params", success3)
        ]
        
        working_tests = [name for name, success in tests if success]
        failing_tests = [name for name, success in tests if not success]
        
        print(f"✅ Working tests: {len(working_tests)}")
        for test_name in working_tests:
            print(f"   - {test_name}")
            
        print(f"❌ Failing tests: {len(failing_tests)}")
        for test_name in failing_tests:
            print(f"   - {test_name}")
        
        if len(working_tests) == 0:
            print("\n🚨 ALL TESTS FAILED - CREDENTIALS EXPIRED")
            print("Solutions:")
            print("1. Log into Shopee Seller Center in browser")
            print("2. Extract fresh authorization code and cookies")
            print("3. Update config.json or use /auth endpoint")
            print("4. Use browser extension to auto-update credentials")
        elif len(failing_tests) > 0:
            print(f"\n⚠️  PARTIAL FAILURE - Some endpoints not working")
            print("This might indicate specific endpoint issues or permission problems")
        else:
            print(f"\n✅ ALL TESTS PASSED - Credentials are working!")
            
    except Exception as e:
        print(f"❌ Script Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
