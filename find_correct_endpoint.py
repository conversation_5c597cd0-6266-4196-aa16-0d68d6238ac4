#!/usr/bin/env python3
"""
Find the correct endpoint for getting order details.
Tests various possible URLs and methods.
"""
import sys
import pathlib
import json
import requests

# Add ShopeeAPI to path
shopee_api_path = str(pathlib.Path(__file__).parent / "ShopeeAPI")
if shopee_api_path not in sys.path:
    sys.path.append(shopee_api_path)

def load_config():
    """Load config from ShopeeAPI/config.json"""
    config_path = pathlib.Path(__file__).parent / "ShopeeAPI" / "config.json"
    with open(config_path, 'r') as f:
        return json.load(f)

def test_endpoint(url, headers, payload, method="POST"):
    """Test an endpoint with given parameters."""
    print(f"\n🔍 Testing: {method} {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        if method == "POST":
            response = requests.post(url, headers=headers, json=payload, timeout=30)
        else:
            response = requests.get(url, headers=headers, params=payload, timeout=30)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ SUCCESS - Keys: {list(data.keys())}")
                if 'data' in data and isinstance(data['data'], dict):
                    print(f"Data keys: {list(data['data'].keys())}")
                return True, data
            except:
                print(f"✅ SUCCESS - Non-JSON: {response.text[:100]}...")
                return True, response.text
        elif response.status_code == 404:
            print(f"❌ 404 - Endpoint not found")
        elif response.status_code == 403:
            print(f"❌ 403 - Forbidden")
        else:
            print(f"❌ {response.status_code} - {response.text[:100]}...")
        
        return False, response.text
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False, str(e)

def main():
    """Main function to find correct endpoint."""
    print("🔍 Finding Correct Shopee Order Details Endpoint")
    print("=" * 60)
    
    config = load_config()
    
    # Prepare headers
    headers = {
        'Authorization': config['AUTHORIZATION_CODE'],
        'Cookie': config['COOKIE'],
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://seller.shopee.com.my/',
        'Origin': 'https://seller.shopee.com.my'
    }
    
    shop_id = config['SHOP_ID']
    region_id = config['REGION_ID']
    
    # First, let's get a real order from your shop
    print("\n📋 Step 1: Getting real orders from your shop...")
    
    search_payload = {
        "order_list_tab": 100,
        "search_keyword": "",
        "page_number": 1,
        "page_size": 5
    }
    
    success, result = test_endpoint(
        "https://seller.shopee.com.my/api/v3/order/search_order_list_index",
        headers,
        search_payload
    )
    
    real_orders = []
    if success and isinstance(result, dict) and 'data' in result:
        data = result['data']
        if 'card_list' in data and data['card_list']:
            for order in data['card_list']:
                if 'package_level_order_card' in order:
                    card = order['package_level_order_card']
                    order_sn = card['card_header']['order_sn']
                    order_id = card['order_ext_info']['order_id']
                    real_orders.append({'order_sn': order_sn, 'order_id': order_id})
                elif 'order_card' in order:
                    card = order['order_card']
                    order_sn = card['card_header']['order_sn']
                    order_id = card['order_ext_info']['order_id']
                    real_orders.append({'order_sn': order_sn, 'order_id': order_id})
    
    if real_orders:
        print(f"✅ Found {len(real_orders)} real orders:")
        for i, order in enumerate(real_orders[:3]):  # Show first 3
            print(f"   {i+1}. Order SN: {order['order_sn']}, ID: {order['order_id']}")
        
        # Use the first real order for testing
        test_order = real_orders[0]
        test_order_sn = test_order['order_sn']
        test_order_id = test_order['order_id']
    else:
        print("❌ No real orders found, using example order")
        test_order_sn = "250604MPB2VBWR"
        test_order_id = 202711189213112
    
    print(f"\n🎯 Step 2: Testing endpoints with Order SN: {test_order_sn}, ID: {test_order_id}")
    
    # Test various possible endpoints
    endpoints_to_test = [
        # Original endpoints
        ("get_one_order", "https://seller.shopee.com.my/api/v3/order/get_one_order"),
        ("get_order_detail", "https://seller.shopee.com.my/api/v3/order/get_order_detail"),
        
        # Possible alternative endpoints
        ("order_detail", "https://seller.shopee.com.my/api/v3/order/order_detail"),
        ("order_info", "https://seller.shopee.com.my/api/v3/order/order_info"),
        ("get_order", "https://seller.shopee.com.my/api/v3/order/get_order"),
        ("order_details", "https://seller.shopee.com.my/api/v3/order/order_details"),
        
        # Different API versions
        ("get_one_order_v2", "https://seller.shopee.com.my/api/v2/order/get_one_order"),
        ("get_one_order_v4", "https://seller.shopee.com.my/api/v4/order/get_one_order"),
        
        # Different paths
        ("orders_get_one", "https://seller.shopee.com.my/api/v3/orders/get_one"),
        ("order_get_detail", "https://seller.shopee.com.my/api/v3/order/get_detail"),
    ]
    
    working_endpoints = []
    
    for name, url in endpoints_to_test:
        print(f"\n{'='*60}")
        print(f"Testing: {name}")
        
        # Test with order_sn
        payload_sn = {
            "order_sn": test_order_sn,
            "shop_id": shop_id,
            "region_id": region_id
        }
        
        success_sn, result_sn = test_endpoint(url, headers, payload_sn)
        
        if success_sn:
            working_endpoints.append((name + "_sn", url, payload_sn))
            print(f"✅ {name} works with order_sn!")
            continue
        
        # Test with order_id
        payload_id = {
            "order_id": test_order_id,
            "shop_id": shop_id,
            "region_id": region_id
        }
        
        success_id, result_id = test_endpoint(url, headers, payload_id)
        
        if success_id:
            working_endpoints.append((name + "_id", url, payload_id))
            print(f"✅ {name} works with order_id!")
            continue
        
        # Test with both
        payload_both = {
            "order_sn": test_order_sn,
            "order_id": test_order_id,
            "shop_id": shop_id,
            "region_id": region_id
        }
        
        success_both, result_both = test_endpoint(url, headers, payload_both)
        
        if success_both:
            working_endpoints.append((name + "_both", url, payload_both))
            print(f"✅ {name} works with both parameters!")
    
    # Test the working batch endpoint with single order
    print(f"\n{'='*60}")
    print("Testing: Batch endpoint with single order")
    
    batch_payload = {
        "order_list_tab": 100,
        "order_param_list": [
            {
                "order_id": test_order_id,
                "shop_id": shop_id,
                "region_id": region_id
            }
        ]
    }
    
    success_batch, result_batch = test_endpoint(
        "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list",
        headers,
        batch_payload
    )
    
    if success_batch:
        working_endpoints.append(("batch_single", "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list", batch_payload))
        print("✅ Batch endpoint works for single order!")
    
    # Summary
    print(f"\n{'='*60}")
    print("RESULTS")
    print(f"{'='*60}")
    
    if working_endpoints:
        print(f"✅ Found {len(working_endpoints)} working endpoints:")
        for name, url, payload in working_endpoints:
            print(f"\n🎯 {name}:")
            print(f"   URL: {url}")
            print(f"   Payload: {json.dumps(payload, indent=6)}")
    else:
        print("❌ No working endpoints found for getting single order details")
        print("\nRecommendations:")
        print("1. Use the batch endpoint with single order")
        print("2. Check Shopee API documentation for changes")
        print("3. Contact Shopee support about API changes")
    
    # Save results
    with open("working_endpoints.json", "w") as f:
        json.dump([{"name": name, "url": url, "payload": payload} for name, url, payload in working_endpoints], f, indent=2)
    
    print(f"\n📄 Results saved to: working_endpoints.json")

if __name__ == "__main__":
    main()
