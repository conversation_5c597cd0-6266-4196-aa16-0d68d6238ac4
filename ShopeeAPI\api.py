"""
FastAPI application for Shopee API.
"""
from typing import Dict, Any, Optional, Union
from fastapi import FastAP<PERSON>, HTTPException, Depends, Query, status, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from .middleware import AuthenticationMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import os
import json
import time
from datetime import datetime
import uuid
import pathlib

# Define global variable for watchdog availability
# This is now handled in fixed_lifespan.py

# Try different import approaches to handle both package and direct imports
try:
    from .client import ShopeeAPI  # When imported as a package
except ImportError:
    try:
        from ShopeeAPI.client import ShopeeAPI  # When imported as a module
    except ImportError:
        import sys
        import os
        # Add parent directory to path if needed
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        # Direct import
        from ShopeeAPI.client import ShopeeAPI

# File watcher variables
observer = None
event_handler = None

# Import the fixed lifespan function
from .fixed_lifespan import fixed_lifespan

# Initialize FastAPI app
app = FastAPI(
    title="Shopee API",
    description="REST API for interacting with Shopee Seller API",
    version="1.0.0",
    lifespan=fixed_lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.add_middleware(AuthenticationMiddleware)

# Get the directory of the current file
current_dir = pathlib.Path(__file__).parent.absolute()

# Configure static files
app.mount("/static", StaticFiles(directory=os.path.join(current_dir, "static")), name="static")

# Configure templates
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))

# Helper function for consistent error handling
def handle_api_error(e: Exception) -> None:
    """
    Handle API errors consistently across endpoints.

    Args:
        e: The exception that was raised

    Raises:
        HTTPException: With appropriate status code and details
    """
    # Check if it's an authentication error
    if hasattr(e, '__class__') and e.__class__.__name__ == 'AuthenticationError':
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "status": "error",
                "error": "Authentication failed",
                "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                "code": "CREDENTIALS_EXPIRED",
                "original_error": str(e)
            }
        )
    # Check if it's a request error with 403 in the message
    elif hasattr(e, '__class__') and e.__class__.__name__ == 'RequestError' and ('403' in str(e) or 'unauthorized' in str(e).lower()):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={
                "status": "error",
                "error": "Authentication failed",
                "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                "code": "CREDENTIALS_EXPIRED",
                "original_error": str(e)
            }
        )
    # Re-raise HTTPExceptions
    elif isinstance(e, HTTPException):
        raise
    # Other errors
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An error occurred: {str(e)}"
        )

# Models
class Credentials(BaseModel):
    authorization_code: str
    cookie: Any  # Can be string or JSON object

class Config(BaseModel):
    shop_id: Optional[int] = None
    region_id: Optional[str] = None

class OrderResponse(BaseModel):
    data: Dict[str, Any]

class StatusResponse(BaseModel):
    order_sn: str
    status: str

class ChatMessagePayload(BaseModel):
    text: str
    username: str
    force_send_cancel_order_warning: Optional[bool] = False
    comply_cancel_order_warning: Optional[bool] = False

class ImageMessagePayload(BaseModel):
    username: str
    image_url: str

class OrderMessagePayload(BaseModel):
    order_sn: str

class ChatResponse(BaseModel):
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class BuyerOrdersResponse(BaseModel):
    orders: list

# API instance
_api_instance = None
_config_last_modified = 0
# Ensure we're using the config.json file in the ShopeeAPI directory
_config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config.json')
print(f"ShopeeAPI using config path: {_config_path}")

# Import FileSystemEventHandler for ConfigFileHandler
try:
    from watchdog.events import FileSystemEventHandler

    class ConfigFileHandler(FileSystemEventHandler):
        """
        Handler for config.json file changes.
        """
        def on_modified(self, event):
            if not event.is_directory and event.src_path.endswith('config.json'):
                print(f"Config file modified: {event.src_path}")
                # Force reload to ensure changes are picked up
                reload_api_from_config(force=True)
except ImportError:
    # Define a dummy class if watchdog is not available
    class ConfigFileHandler:
        """Dummy handler when watchdog is not available."""
        pass

def reload_api_from_config(force=False):
    """
    Reload the API instance from the config file.

    Args:
        force: If True, reload even if the file hasn't been modified
    """
    global _api_instance, _config_last_modified

    # Check if the file has been modified since last reload
    try:
        if not os.path.exists(_config_path):
            print(f"Warning: Config file {_config_path} does not exist")
            return

        current_mtime = os.path.getmtime(_config_path)
        if not force and current_mtime <= _config_last_modified:
            return  # File hasn't changed and not forced

        _config_last_modified = current_mtime
        print(f"Loading config from {_config_path}" + (" (forced)" if force else ""))

        # Load credentials from config file
        credentials = {}
        try:
            with open(_config_path, 'r') as f:
                credentials = json.load(f)
            print(f"Reloaded config from {_config_path}")
        except json.JSONDecodeError:
            print(f"Error: Could not parse {_config_path}")
            return
        except Exception as e:
            print(f"Error reading config file: {e}")
            return

        # If we already have an instance, update it
        if _api_instance is not None:
            try:
                # Adapt to any API changes first
                _api_instance.adapt_to_api_changes()

                # Update credentials
                cookie = credentials.get('COOKIE_JSON', credentials.get('COOKIE', ''))
                is_valid = _api_instance.update_credentials(
                    credentials.get('AUTHORIZATION_CODE', ''),
                    cookie
                )

                # Update other configuration
                if 'SHOP_ID' in credentials:
                    _api_instance.config.shop_id = credentials['SHOP_ID']
                if 'REGION_ID' in credentials:
                    _api_instance.config.region_id = credentials['REGION_ID']

                try:
                    if is_valid:
                        print("Updated API instance with new valid credentials")

                        # Reconnect WebSocket if enabled
                        if _api_instance.config.websocket["enabled"] and hasattr(_api_instance, 'websocket_service'):
                            # We need to schedule a reconnection, but we're in a sync function
                            # So we'll set a flag that will be checked by the maintain_connection loop
                            try:
                                print("Marking WebSocket for reconnection after config update...")
                                # Set a flag that will be checked by the maintain_connection loop
                                _api_instance.websocket_service.needs_reconnect_after_config_update = True
                            except Exception as e:
                                print(f"Error marking WebSocket for reconnection: {e}")
                    else:
                        print("Warning: Updated API instance but credentials may be invalid")
                except Exception as e:
                    print(f"Error validating credentials: {str(e)}")
                    print("Warning: Updated API instance but credentials validation failed")
            except Exception as e:
                print(f"Error updating API instance: {e}")
        else:
            # Initialize API client
            try:
                # Get cookie (prefer JSON format if available)
                cookie = credentials.get('COOKIE_JSON', credentials.get('COOKIE', ''))

                _api_instance = ShopeeAPI(
                    authorization_code=credentials.get('AUTHORIZATION_CODE', ''),
                    cookie=cookie,
                    shop_id=credentials.get('SHOP_ID'),
                    region_id=credentials.get('REGION_ID', 'MY')
                )

                # Adapt to any API changes first
                _api_instance.adapt_to_api_changes()

                # Validate credentials
                try:
                    if _api_instance.check_credentials():
                        print("Created new API instance from config with valid credentials")
                    else:
                        print("Warning: Created new API instance but credentials may be invalid")
                except Exception as e:
                    print(f"Error validating credentials: {str(e)}")
                    print("Warning: Created new API instance but credentials validation failed")
            except Exception as e:
                print(f"Error creating API instance: {e}")
    except Exception as e:
        print(f"Error reloading config: {e}")



def get_api() -> ShopeeAPI:
    """
    Get or create a ShopeeAPI instance.
    Reads credentials from config.json if available.

    Returns:
        ShopeeAPI instance

    Raises:
        HTTPException: If credentials are invalid or missing
    """
    global _api_instance

    if _api_instance is None:
        # Force reload on first access
        reload_api_from_config(force=True)

    # If still None after reload attempt, create a default instance
    if _api_instance is None:
        print("Warning: Creating default API instance because config.json could not be loaded")
        _api_instance = ShopeeAPI()

    # Check if credentials are valid
    try:
        if not _api_instance.check_credentials():
            print("Warning: API instance has invalid credentials")
            # Try to force reload the config in case it was updated
            reload_api_from_config(force=True)
            # Check again after reload
            if not _api_instance.check_credentials():
                print("Warning: Credentials still invalid after reload")
            else:
                print("Credentials valid after forced reload")
    except Exception as e:
        print(f"Error checking credentials: {str(e)}")
        # Continue anyway, endpoints will handle auth errors

    return _api_instance

@app.get("/")
async def root():
    """API root endpoint"""
    return {"message": "Shopee API Service", "docs_url": "/docs"}

@app.get("/ws-docs", response_class=HTMLResponse)
async def get_ws_docs(request: Request):
    """WebSocket documentation and testing page"""
    return templates.TemplateResponse("ws_docs.html", {"request": request})

@app.get("/status")
async def check_status(api: ShopeeAPI = Depends(get_api)):
    """
    Check the status of the API and credentials.
    """
    try:
        # Check if credentials are valid
        is_valid = api.check_credentials()

        # Try to make a simple API call to verify connectivity
        connectivity_ok = False
        error_message = None

        try:
            # Attempt to get a list of orders (will be empty if no orders, but should not error if credentials are valid)
            api.get_to_ship_orders()
            connectivity_ok = True
        except Exception as e:
            error_message = str(e)

        # Check WebSocket connection status
        websocket_enabled = api.config.websocket["enabled"]
        websocket_connected = api.websocket_service.is_connected if websocket_enabled else False

        return {
            "status": "ok" if is_valid and connectivity_ok else "error",
            "credentials_valid": is_valid,
            "connectivity_ok": connectivity_ok,
            "websocket_enabled": websocket_enabled,
            "websocket_connected": websocket_connected,
            "error": error_message,
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "status": "error",
            "credentials_valid": False,
            "connectivity_ok": False,
            "websocket_enabled": False,
            "websocket_connected": False,
            "error": str(e),
            "timestamp": time.time()
        }

@app.get("/auth/check")
async def check_auth(api: ShopeeAPI = Depends(get_api)):
    """
    Check if current credentials are valid and return expiration information.
    """
    is_valid = api.check_credentials()

    # Get expiration timestamp for SPC_STK cookie
    expiration_timestamp = None
    expiration_date = None

    try:
        # Get expiration from credential manager
        expiration_timestamp = api.credential_manager.get_cookie_expiration("SPC_STK")

        # Convert to human-readable date if available
        if expiration_timestamp:
            expiration_date = datetime.fromtimestamp(expiration_timestamp).isoformat()
    except Exception as e:
        print(f"Error getting cookie expiration: {e}")

    return {
        "valid": is_valid,
        "expiration_timestamp": expiration_timestamp,
        "expiration_date": expiration_date,
        "current_timestamp": time.time()
    }

@app.get("/websocket/status")
async def websocket_status(api: ShopeeAPI = Depends(get_api)):
    """
    Check the status of the WebSocket connection to Shopee.
    """
    websocket_enabled = api.config.websocket["enabled"]
    websocket_connected = api.websocket_service.is_connected if websocket_enabled else False
    last_message_time = None

    if websocket_enabled and api.websocket_service.last_message_time:
        last_message_time = api.websocket_service.last_message_time.timestamp()

    return {
        "enabled": websocket_enabled,
        "connected": websocket_connected,
        "last_message_time": last_message_time,
        "reconnect_attempts": api.websocket_service.reconnect_attempt if websocket_enabled else 0,
        "client_count": len(api.websocket_service.connected_clients) if websocket_enabled else 0,
        "timestamp": time.time()
    }

@app.post("/websocket/reconnect")
async def websocket_reconnect(api: ShopeeAPI = Depends(get_api)):
    """
    Force reconnect the WebSocket connection to Shopee.
    """
    if not api.config.websocket["enabled"]:
        raise HTTPException(status_code=400, detail="WebSocket is not enabled in configuration")

    try:
        # Check if sio attribute exists before trying to disconnect
        if hasattr(api.websocket_service, 'sio') and hasattr(api.websocket_service.sio, 'connected') and api.websocket_service.sio.connected:
            await api.websocket_service.sio.disconnect()

        # Reset reconnect attempt counter
        api.websocket_service.reconnect_attempt = 0

        # Try to connect
        success = await api.websocket_service.connect_to_shopee()

        return {
            "success": success,
            "connected": api.websocket_service.is_connected,
            "message": "WebSocket reconnection initiated" if success else "WebSocket reconnection failed",
            "timestamp": time.time()
        }
    except Exception as e:
        return {
            "success": False,
            "connected": False,
            "message": f"Error reconnecting WebSocket: {str(e)}",
            "timestamp": time.time()
        }

@app.post("/auth/reload")
async def reload_auth():
    """
    Force reload credentials from config.json.
    """
    reload_api_from_config(force=True)
    api = get_api()
    is_valid = api.check_credentials()

    # Reconnect WebSocket if enabled
    websocket_reconnected = False
    if api.config.websocket["enabled"] and hasattr(api, 'websocket_service'):
        try:
            print("Manually reconnecting WebSocket after config reload...")
            # Since we're in an async function, we can await the reconnect directly
            websocket_reconnected = await api.websocket_service.reconnect_after_config_update()
        except Exception as e:
            print(f"Error reconnecting WebSocket: {e}")

    return {
        "message": "Config reloaded",
        "valid": is_valid,
        "config_path": _config_path,
        "config_exists": os.path.exists(_config_path),
        "last_modified": _config_last_modified,
        "websocket_reconnected": websocket_reconnected
    }

@app.post("/auth", status_code=status.HTTP_200_OK)
async def update_credentials(credentials: Credentials, api: ShopeeAPI = Depends(get_api)):
    """
    Update API credentials.
    """
    # Update the API instance directly and validate credentials
    is_valid = api.update_credentials(credentials.authorization_code, credentials.cookie)

    if not is_valid:
        print("Warning: Credentials may be invalid or incomplete")
        # We'll still save them but warn the user

    # Save to config.json
    config_data = {}

    if os.path.exists(_config_path):
        try:
            with open(_config_path, 'r') as f:
                config_data = json.load(f)
        except json.JSONDecodeError:
            pass

    config_data['AUTHORIZATION_CODE'] = credentials.authorization_code

    # Handle both string and JSON cookies
    if isinstance(credentials.cookie, dict) or isinstance(credentials.cookie, list):
        # Store as JSON object
        config_data['COOKIE_JSON'] = credentials.cookie
        # Also store as string for backward compatibility
        if hasattr(api.credential_manager, '_cookie_str'):
            config_data['COOKIE'] = api.credential_manager._cookie_str
    else:
        # Store as string
        config_data['COOKIE'] = credentials.cookie

        # Try to parse the cookie string into JSON format if it's not already in JSON format
        try:
            # Check if the cookie string contains key-value pairs
            if credentials.cookie and ';' in credentials.cookie:
                cookie_dict = {}
                cookie_parts = credentials.cookie.split(';')
                for part in cookie_parts:
                    part = part.strip()
                    if '=' in part:
                        name, value = part.split('=', 1)
                        # URL decode the value if needed (especially for CTOKEN)
                        if name == 'CTOKEN' and '%' in value:
                            import urllib.parse
                            value = urllib.parse.unquote(value)
                        cookie_dict[name] = value

                # Store the parsed JSON
                if cookie_dict:
                    config_data['COOKIE_JSON'] = cookie_dict
                    print(f"Successfully parsed cookie string into JSON format with {len(cookie_dict)} cookies")
        except Exception as e:
            print(f"Error parsing cookie string to JSON: {str(e)}")
            # If parsing fails, we'll still keep the string version

    try:
        # Write to config.json
        with open(_config_path, 'w') as f:
            json.dump(config_data, f, indent=2)

        # Update the last modified time to prevent duplicate reloads
        global _config_last_modified
        _config_last_modified = os.path.getmtime(_config_path)

        print(f"Updated config.json with new credentials")
    except PermissionError as e:
        print(f"Permission error saving config: {e}")

        # Try to fix permissions using sudo if we're in Docker
        if os.path.exists('/app/fix_permissions.sh'):
            try:
                print("Attempting to fix permissions...")
                import subprocess
                result = subprocess.run(['sudo', '/app/fix_permissions.sh'],
                                       capture_output=True, text=True)

                if result.returncode == 0:
                    print("Permissions fixed, trying to save again")
                    try:
                        # Try writing again
                        with open(_config_path, 'w') as f:
                            json.dump(config_data, f, indent=2)

                        # Update the last modified time
                        _config_last_modified = os.path.getmtime(_config_path)
                        print(f"Successfully saved config after fixing permissions")
                        return {"message": "Credentials updated successfully", "valid": is_valid}
                    except Exception as e2:
                        print(f"Still failed to save config after fixing permissions: {e2}")
                        return {
                            "message": "Credentials saved but with errors",
                            "error": f"Permission fix attempted but still failed: {str(e2)}",
                            "valid": is_valid
                        }
                else:
                    print(f"Failed to fix permissions: {result.stderr}")
                    return {
                        "message": "Credentials saved but with errors",
                        "error": f"Permission fix failed: {result.stderr}",
                        "valid": is_valid
                    }
            except Exception as e3:
                print(f"Error running permission fix: {e3}")
                return {
                    "message": "Credentials saved but with errors",
                    "error": f"Permission fix error: {str(e3)}",
                    "valid": is_valid
                }

        return {"message": "Credentials saved but with errors", "error": str(e), "valid": is_valid}
    except Exception as e:
        print(f"Error saving config: {e}")
        return {"message": "Credentials saved but with errors", "error": str(e), "valid": is_valid}

    return {"message": "Credentials updated successfully", "valid": is_valid}

@app.post("/config", status_code=status.HTTP_200_OK)
async def update_config(config: Config, api: ShopeeAPI = Depends(get_api)):
    """
    Update API configuration.
    """
    # Update the API instance directly
    if config.shop_id is not None:
        api.config.shop_id = config.shop_id
    if config.region_id is not None:
        api.config.region_id = config.region_id

    # Save to config.json
    config_data = {}

    if os.path.exists(_config_path):
        try:
            with open(_config_path, 'r') as f:
                config_data = json.load(f)
        except json.JSONDecodeError:
            pass

    if config.shop_id is not None:
        config_data['SHOP_ID'] = config.shop_id
    if config.region_id is not None:
        config_data['REGION_ID'] = config.region_id

    try:
        # Write to config.json
        with open(_config_path, 'w') as f:
            json.dump(config_data, f, indent=2)

        # Update the last modified time to prevent duplicate reloads
        global _config_last_modified
        _config_last_modified = os.path.getmtime(_config_path)

        print(f"Updated config.json with new configuration")
    except PermissionError as e:
        print(f"Permission error saving config: {e}")

        # Try to fix permissions using sudo if we're in Docker
        if os.path.exists('/app/fix_permissions.sh'):
            try:
                print("Attempting to fix permissions...")
                import subprocess
                result = subprocess.run(['sudo', '/app/fix_permissions.sh'],
                                       capture_output=True, text=True)

                if result.returncode == 0:
                    print("Permissions fixed, trying to save again")
                    try:
                        # Try writing again
                        with open(_config_path, 'w') as f:
                            json.dump(config_data, f, indent=2)

                        # Update the last modified time
                        _config_last_modified = os.path.getmtime(_config_path)
                        print(f"Successfully saved config after fixing permissions")
                        return {"message": "Configuration updated successfully"}
                    except Exception as e2:
                        print(f"Still failed to save config after fixing permissions: {e2}")
                        return {
                            "message": "Configuration updated in memory but not saved to file",
                            "error": f"Permission fix attempted but still failed: {str(e2)}"
                        }
                else:
                    print(f"Failed to fix permissions: {result.stderr}")
                    return {
                        "message": "Configuration updated in memory but not saved to file",
                        "error": f"Permission fix failed: {result.stderr}"
                    }
            except Exception as e3:
                print(f"Error running permission fix: {e3}")
                return {
                    "message": "Configuration updated in memory but not saved to file",
                    "error": f"Permission fix error: {str(e3)}"
                }

        return {
            "message": "Configuration updated in memory but not saved to file",
            "error": str(e)
        }
    except Exception as e:
        print(f"Error saving config: {e}")
        return {
            "message": "Configuration updated in memory but not saved to file",
            "error": str(e)
        }

    return {"message": "Configuration updated successfully"}

@app.get("/orders/to_ship", response_model=OrderResponse)
async def get_to_ship_orders(api: ShopeeAPI = Depends(get_api)):
    """
    Get orders with 'To Ship' status.
    """
    return api.get_to_ship_orders()

@app.get("/orders/shipped", response_model=OrderResponse)
async def get_shipped_orders(api: ShopeeAPI = Depends(get_api)):
    """
    Get orders with 'Shipped' status.
    """
    return api.get_shipped_orders()

@app.get("/orders/completed", response_model=OrderResponse)
async def get_completed_orders(api: ShopeeAPI = Depends(get_api)):
    """
    Get orders with 'Completed' status.
    """
    return api.get_completed_orders()

@app.get("/orders/search", response_model=OrderResponse)
async def search_order(order_sn: str = Query(..., description="Order number to search for"),
                       api: ShopeeAPI = Depends(get_api)):
    """
    Search for an order by order number.
    """
    try:
        return api.search_order(order_sn)
    except Exception as e:
        handle_api_error(e)

@app.get("/orders/{order_sn}/status", response_model=StatusResponse)
async def get_order_status(order_sn: str, api: ShopeeAPI = Depends(get_api)):
    """
    Get status of an order.
    """
    try:
        status_data, status_code = api.get_order_status(order_sn)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": status_data.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=status_data.get("error", "Unknown error"))

        return status_data
    except Exception as e:
        handle_api_error(e)

@app.get("/orders/{order_sn}/details")
async def get_order_details(order_sn: str, api: ShopeeAPI = Depends(get_api)):
    """
    Get detailed information for a specific order.
    """
    try:
        details_data, status_code = api.get_order_details(order_sn)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": details_data.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=details_data.get("error", "Unknown error"))

        return details_data
    except Exception as e:
        handle_api_error(e)

@app.get("/orders/{order_sn}/details/direct")
async def get_order_details_direct(order_sn: str, api: ShopeeAPI = Depends(get_api)):
    """
    Get detailed information for a specific order using direct API call without fallback.
    This endpoint directly calls the order_details_specific API to get complete order information.
    """
    try:
        # Get the order service
        order_service = api.order_service

        # Try direct API call with order_sn first
        payload_with_sn = {
            "order_sn": order_sn,
            "shop_id": order_service.config.shop_id,
            "region_id": order_service.config.region_id
        }

        response = order_service.session.post(
            order_service.config.urls["order_details_specific"],
            params=order_service.session.get_common_params(),
            json=payload_with_sn
        )

        if response.status_code == 200:
            response_data = response.json()
            if not response_data.get('error'):
                return {
                    "data": response_data,
                    "method": "direct_order_sn",
                    "payload": payload_with_sn
                }

        # If order_sn doesn't work, try to get order_id first
        initial_data = order_service.get_initial_order_list("all", order_sn)
        order_data = order_service.process_orders(initial_data, "all")

        if not order_data['data']['card_list']:
            raise HTTPException(status_code=404, detail=f"Order not found: {order_sn}")

        order = order_data['data']['card_list'][0]

        # Extract order ID
        order_id = None
        if 'package_level_order_card' in order:
            order_id = order['package_level_order_card']['order_ext_info']['order_id']
        elif 'order_card' in order:
            order_id = order['order_card']['order_ext_info']['order_id']
        else:
            raise HTTPException(status_code=500, detail="Unexpected order structure")

        # Try with order_id
        payload_with_id = {
            "order_id": order_id,
            "shop_id": order_service.config.shop_id,
            "region_id": order_service.config.region_id
        }

        response = order_service.session.post(
            order_service.config.urls["order_details_specific"],
            params=order_service.session.get_common_params(),
            json=payload_with_id
        )

        if response.status_code == 200:
            response_data = response.json()
            if not response_data.get('error'):
                return {
                    "data": response_data,
                    "method": "order_id_lookup",
                    "payload": payload_with_id,
                    "order_id": order_id
                }

        # Return debug information if both methods fail
        return {
            "error": "Both direct methods failed",
            "debug_info": {
                "order_sn_response": {
                    "status_code": response.status_code,
                    "response": response.text[:500] if response.text else None
                },
                "order_id": order_id,
                "payloads_tried": [payload_with_sn, payload_with_id]
            }
        }

    except Exception as e:
        handle_api_error(e)

@app.get("/orders/{order_sn}/details/debug")
async def debug_order_details(order_sn: str, api: ShopeeAPI = Depends(get_api)):
    """
    Debug endpoint to check why order details might be failing.
    This endpoint provides detailed information about the API calls and authentication status.
    """
    try:
        # Check authentication first
        auth_check_response = {}
        try:
            # Try a simple API call to check if credentials are valid
            order_service = api.order_service
            test_response = order_service.session.post(
                order_service.config.urls["initial_order_list"],
                params=order_service.session.get_common_params(),
                json={
                    "order_list_tab": 100,  # All orders
                    "search_keyword": order_sn,
                    "page_number": 1,
                    "page_size": 1
                }
            )
            auth_check_response = {
                "status": "success" if test_response.status_code == 200 else "failed",
                "status_code": test_response.status_code,
                "credentials_valid": test_response.status_code != 403
            }
        except Exception as e:
            auth_check_response = {
                "status": "error",
                "error": str(e),
                "credentials_valid": False
            }

        # Try the order details call with debugging
        debug_info = {
            "order_sn": order_sn,
            "auth_check": auth_check_response,
            "api_attempts": []
        }

        # Attempt 1: Direct call with order_sn
        try:
            payload_with_sn = {
                "order_sn": order_sn,
                "shop_id": order_service.config.shop_id,
                "region_id": order_service.config.region_id
            }

            response = order_service.session.post(
                order_service.config.urls["order_details_specific"],
                params=order_service.session.get_common_params(),
                json=payload_with_sn
            )

            attempt_1 = {
                "method": "direct_order_sn",
                "payload": payload_with_sn,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_preview": response.text[:200] if response.text else None
            }

            if response.status_code == 200:
                response_data = response.json()
                attempt_1["has_error"] = bool(response_data.get('error'))
                attempt_1["response_keys"] = list(response_data.keys()) if response_data else []

                if not response_data.get('error'):
                    return {
                        "status": "success",
                        "message": "Order details retrieved successfully",
                        "debug_info": debug_info,
                        "data": response_data
                    }

            debug_info["api_attempts"].append(attempt_1)

        except Exception as e:
            debug_info["api_attempts"].append({
                "method": "direct_order_sn",
                "error": str(e),
                "success": False
            })

        # If we get here, the direct method failed
        return {
            "status": "failed",
            "message": "Order details retrieval failed",
            "debug_info": debug_info,
            "recommendations": [
                "Update your authorization code and cookies if credentials_valid is False",
                "Check if the order_sn exists in your shop",
                "Verify your shop_id and region_id in config.json"
            ]
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "message": "Debug endpoint encountered an error"
        }

@app.get("/buyers/{buyer_id}/orders", response_model=BuyerOrdersResponse)
async def get_buyer_orders(
    buyer_id: int,
    order_type: str = Query("all", description="Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)"),
    page: int = Query(1, description="Page number", ge=1),
    per_page: int = Query(20, description="Number of orders per page", ge=1, le=100),
    api: ShopeeAPI = Depends(get_api)
):
    """
    Get orders from a specific buyer.
    """
    try:
        orders_data, status_code = api.get_buyer_orders(buyer_id, order_type, page, per_page)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": orders_data.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=orders_data.get("error", "Unknown error"))

        return orders_data
    except Exception as e:
        handle_api_error(e)

@app.get("/buyers/username/{username}/orders", response_model=BuyerOrdersResponse)
async def get_buyer_orders_by_username(
    username: str,
    order_type: str = Query("all", description="Order type filter (all, to_ship, unpaid, shipping, completed, cancelled, refund)"),
    page: int = Query(1, description="Page number", ge=1),
    per_page: int = Query(20, description="Number of orders per page", ge=1, le=100),
    api: ShopeeAPI = Depends(get_api)
):
    """
    Get orders from a specific buyer by username.

    This endpoint first finds the buyer ID for the given username using the conversation mapping,
    then retrieves the orders for that buyer.
    """
    try:
        orders_data, status_code = api.get_buyer_orders_by_username(username, order_type, page, per_page)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": orders_data.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=orders_data.get("error", "Unknown error"))

        return orders_data
    except Exception as e:
        handle_api_error(e)

@app.post("/orders/{order_sn}/ship")
async def ship_order(order_sn: str, api: ShopeeAPI = Depends(get_api)):
    """
    Ship an order.
    """
    try:
        ship_data, status_code = api.ship_order(order_sn)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": ship_data.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=ship_data.get("error", "Unknown error"))

        return ship_data
    except Exception as e:
        handle_api_error(e)

# Chat-related endpoints
@app.post("/chat/send_message", response_model=ChatResponse)
async def send_chat_message(payload: ChatMessagePayload, api: ShopeeAPI = Depends(get_api)):
    """
    Send a text message to a user.
    """
    try:
        response, status_code = api.send_chat_message(payload.model_dump())

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket, api: ShopeeAPI = Depends(get_api)):
    """
    WebSocket endpoint for real-time chat messages.

    This endpoint allows clients to receive real-time messages from Shopee
    without having to repeatedly poll the server.
    """
    client_id = str(uuid.uuid4())

    try:
        # Accept the WebSocket connection
        await websocket.accept()

        # Send initial connection message
        await websocket.send_json({
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": time.time()
        })

        # Register the client with the WebSocket service
        await api.websocket_service.handle_client(client_id, websocket)
    except WebSocketDisconnect:
        # Client disconnected
        print(f"WebSocket client {client_id} disconnected")
    except Exception as e:
        # Handle other errors
        print(f"Error in WebSocket connection: {e}")
        try:
            await websocket.close(code=1011, reason=f"Internal server error: {str(e)}")
        except:
            pass

@app.websocket("/ws/chat/{username}")
async def websocket_chat_username_endpoint(websocket: WebSocket, username: str, api: ShopeeAPI = Depends(get_api)):
    """
    WebSocket endpoint for real-time chat messages filtered by username.

    This endpoint allows clients to receive real-time messages from a specific
    conversation with the given username.

    Args:
        username: The username to filter messages for
    """
    client_id = str(uuid.uuid4())

    try:
        # Accept the WebSocket connection
        await websocket.accept()

        # Try to get the conversation ID for this username
        conversation_info, error = api.chat_service.get_conversation_info_by_username(username)

        if error:
            # Send error message
            await websocket.send_json({
                "type": "error",
                "message": f"Could not find conversation for username: {username}",
                "error": error,
                "timestamp": time.time()
            })
            await websocket.close(code=1008, reason=f"Conversation not found: {username}")
            return

        # Send initial connection message
        await websocket.send_json({
            "type": "connection_established",
            "client_id": client_id,
            "username": username,
            "conversation_id": conversation_info.get("conversation_id"),
            "timestamp": time.time()
        })

        # Register the client with the WebSocket service
        # In the future, we could filter messages by username here
        await api.websocket_service.handle_client(client_id, websocket)
    except WebSocketDisconnect:
        # Client disconnected
        print(f"WebSocket client {client_id} for username {username} disconnected")
    except Exception as e:
        # Handle other errors
        print(f"Error in WebSocket connection for username {username}: {e}")
        try:
            await websocket.close(code=1011, reason=f"Internal server error: {str(e)}")
        except:
            pass

@app.post("/chat/send_image", response_model=ChatResponse)
async def send_image_message(payload: ImageMessagePayload, api: ShopeeAPI = Depends(get_api)):
    """
    Send an image message to a user.
    """
    try:
        response, status_code = api.send_image_message(payload.model_dump())

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.post("/chat/send_order_message", response_model=ChatResponse)
async def send_order_message(payload: OrderMessagePayload, api: ShopeeAPI = Depends(get_api)):
    """
    Send an order message to a user.
    """
    print(f"API endpoint: Received request to send order message for order_sn: {payload.order_sn}")

    try:
        # First check if the order exists
        print(f"API endpoint: Checking if order {payload.order_sn} exists")
        order_data = api.search_order(payload.order_sn)
        print(f"API endpoint: Order search result: {order_data}")

        if not order_data.get('data', {}).get('card_list', []):
            print(f"API endpoint: Order {payload.order_sn} not found")
            raise HTTPException(status_code=404, detail=f"Order '{payload.order_sn}' not found")

        # Send the order message
        print(f"API endpoint: Sending order message for {payload.order_sn}")
        response, status_code = api.send_order_message(payload.order_sn)
        print(f"API endpoint: Send order message response: {response}, status_code: {status_code}")

        if status_code != 200:
            print(f"API endpoint: Error sending order message: {response.get('error', 'Unknown error')}")
            raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        print(f"API endpoint: Successfully sent order message for {payload.order_sn}")
        return {"data": response}

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        import traceback
        print(f"API endpoint: Exception in send_order_message: {str(e)}")
        print(traceback.format_exc())
        handle_api_error(e)

@app.get("/chat/conversations/recent", response_model=ChatResponse)
async def get_recent_conversations(
    unread_only: bool = Query(False, description="Whether to only retrieve unread conversations"),
    api: ShopeeAPI = Depends(get_api)
):
    """
    Get a list of recent conversations.

    This endpoint returns a list of recent conversations with basic information about each conversation,
    including the conversation ID, user information, and the latest message preview.

    Use this endpoint to display a list of conversations in a chat interface.

    Parameters:
    - **unread_only**: Whether to only retrieve unread conversations (default: False)
    """
    try:
        response, status_code = api.get_recent_conversations(unread_only)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.get("/chat/conversations/recent_messages", response_model=ChatResponse)
async def get_recent_latest_messages(api: ShopeeAPI = Depends(get_api)):
    """
    Get the latest message from each recent conversation.

    This endpoint extracts and returns only the most recent message from each conversation,
    formatted in a simplified structure. It's useful for displaying message previews or
    notifications without loading full conversation details.

    Unlike the /chat/conversations/recent endpoint, this endpoint focuses on the message content
    rather than conversation metadata.
    """
    try:
        # First check if credentials are valid
        if not api.credential_manager.validate_credentials():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={
                    "status": "error",
                    "error": "Authentication failed",
                    "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                    "code": "CREDENTIALS_EXPIRED"
                }
            )

        messages = api.get_recent_latest_messages()

        # If messages is empty, it might be due to an authentication error
        # Let's check if we can get recent conversations to verify
        if not messages:
            print("No messages returned, checking if we can get recent conversations")
            conversations_response, status_code = api.get_recent_conversations()

            if status_code != 200:
                # If we can't get recent conversations, it's likely an authentication issue
                if status_code in (401, 403):
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail={
                            "status": "error",
                            "error": "Authentication failed",
                            "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                            "code": "CREDENTIALS_EXPIRED",
                            "original_error": conversations_response.get("error", "Unknown error")
                        }
                    )
                else:
                    # Some other error with the recent conversations endpoint
                    raise HTTPException(
                        status_code=status_code,
                        detail=conversations_response.get("error", "Failed to retrieve recent conversations")
                    )

        return {"data": {"messages": messages}}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        import traceback
        print(f"API endpoint: Exception in get_recent_latest_messages: {str(e)}")
        print(traceback.format_exc())
        handle_api_error(e)

@app.get("/chat/conversations/{conversation_id}/messages", response_model=ChatResponse)
async def get_conversation_messages(
    conversation_id: str,
    offset: int = Query(0, description="Number of messages to skip"),
    limit: int = Query(20, description="Maximum number of messages to return", ge=1, le=100),
    direction: str = Query("older", description="Direction to retrieve messages", enum=["older", "newer"]),
    api: ShopeeAPI = Depends(get_api)
):
    """
    Get messages from a specific conversation.

    Parameters:
    - **conversation_id**: ID of the conversation
    - **offset**: Number of messages to skip (default: 0)
    - **limit**: Maximum number of messages to return (default: 20, max: 100)
    - **direction**: Direction to retrieve messages ('older' or 'newer', default: 'older')
    """
    try:
        response, status_code, is_cached = api.get_conversation_messages(conversation_id, offset, limit, direction)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        # Add cached indicator to the response
        if isinstance(response, dict):
            response["cached"] = is_cached
        else:
            response = {"messages": response, "cached": is_cached}

        # Wrap the response in a dictionary if it's a list
        if isinstance(response, list):
            return {"data": {"messages": response, "cached": is_cached}}
        else:
            # If it's already a dictionary, return it as is
            return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.get("/chat/search_conversation", response_model=ChatResponse)
async def search_conversation_by_username(username: str = Query(..., description="Username to search for"),
                                         api: ShopeeAPI = Depends(get_api)):
    """
    Search for a conversation by username.
    """
    try:
        print(f"Searching for conversation with username: {username}")

        conversation_info, error = api.get_conversation_info_by_username(username)

        if error:
            print(f"Error searching conversation: {error}")
            # Check if it's an authentication error
            if isinstance(error, dict) and error.get("error", "").lower() in ["unauthorized", "forbidden", "authentication failed"]:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": error.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=404, detail=error.get("error", "User not found"))

        if not conversation_info:
            print(f"No conversation found for username: {username}")
            raise HTTPException(status_code=404, detail=f"No conversation found for username: {username}")

        print(f"Found conversation for username: {username}")
        return {"data": conversation_info}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        handle_api_error(e)

@app.get("/chat/conversations/username/{username}/messages", response_model=ChatResponse)
async def get_conversation_messages_by_username(
    username: str,
    offset: int = Query(0, description="Number of messages to skip"),
    limit: int = Query(20, description="Maximum number of messages to return", ge=1, le=100),
    direction: str = Query("older", description="Direction to retrieve messages", enum=["older"]),
    force_refresh: bool = Query(False, description="Force refresh from Shopee API instead of using cache"),
    api: ShopeeAPI = Depends(get_api)
):
    """
    Get messages from a conversation with a specific user by username.

    This endpoint first finds the conversation ID for the given username,
    then retrieves the messages for that conversation.

    Parameters:
    - **username**: Username of the user to get conversation messages with
    - **offset**: Number of messages to skip (default: 0)
    - **limit**: Maximum number of messages to return (default: 20, max: 100)
    - **direction**: Direction to retrieve messages (only 'older' is supported, default: 'older')
    - **force_refresh**: Force refresh from Shopee API instead of using cache (default: false)
    """
    try:
        print(f"Getting conversation messages for username: {username} with params: offset={offset}, limit={limit}, direction={direction}, force_refresh={force_refresh}")

        response, status_code, is_cached = api.get_conversation_messages_by_username(username, offset, limit, direction, force_refresh)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        # Add cached indicator to the response
        if isinstance(response, dict):
            response["cached"] = is_cached
        else:
            response = {"messages": response, "cached": is_cached}

        # Wrap the response in a dictionary if it's a list
        if isinstance(response, list):
            return {"data": {"messages": response, "cached": is_cached}}
        else:
            # If it's already a dictionary, return it as is
            return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.put("/chat/conversations/{conversation_id}/unread", response_model=ChatResponse)
async def set_conversation_unread(
    conversation_id: str,
    api: ShopeeAPI = Depends(get_api)
):
    """
    Mark a conversation as unread.

    Parameters:
    - **conversation_id**: ID of the conversation to mark as unread
    """
    try:
        print(f"Marking conversation {conversation_id} as unread")

        response, status_code = api.set_conversation_unread(conversation_id)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.put("/chat/conversations/username/{username}/unread", response_model=ChatResponse)
async def set_conversation_unread_by_username(
    username: str,
    api: ShopeeAPI = Depends(get_api)
):
    """
    Mark a conversation as unread by username.

    This endpoint first finds the conversation ID for the given username,
    then marks that conversation as unread.

    Parameters:
    - **username**: Username of the conversation to mark as unread
    """
    try:
        print(f"Marking conversation with user {username} as unread")

        response, status_code = api.set_conversation_unread_by_username(username)

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.post("/cache/clear", response_model=ChatResponse)
async def clear_cache(api: ShopeeAPI = Depends(get_api)):
    """
    Clear all caches.

    This endpoint clears all caches, including the username to conversation ID cache.
    """
    try:
        print("Clearing all caches")

        response = api.clear_cache()

        return {"data": response}
    except Exception as e:
        handle_api_error(e)

@app.get("/chat/login_info", response_model=ChatResponse)
async def get_chat_login_info(api: ShopeeAPI = Depends(get_api)):
    """
    Get chat login information from Shopee API.

    This endpoint calls the mini/login endpoint to get the chat token and other login information.
    This information is used for WebSocket authentication and can be used to extract key user data.
    """
    try:
        response, status_code = await api.get_chat_login_info()

        if status_code != 200:
            # Check if it's an authentication error
            if status_code in (401, 403):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail={
                        "status": "error",
                        "error": "Authentication failed",
                        "detail": "Your credentials have expired or are invalid. Please update your authorization code and cookie.",
                        "code": "CREDENTIALS_EXPIRED",
                        "original_error": response.get("error", "Unknown error")
                    }
                )
            else:
                raise HTTPException(status_code=status_code, detail=response.get("error", "Unknown error"))

        return {"data": response}
    except Exception as e:
        handle_api_error(e)