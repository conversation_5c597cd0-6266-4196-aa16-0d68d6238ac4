[{"endpoint": "initial_order_list", "url": "https://seller.shopee.com.my/api/v3/order/search_order_list_index", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/search_order_list_index with status code 403", "success": false}, {"endpoint": "order_details", "url": "https://seller.shopee.com.my/api/v3/order/get_order_list_card_list", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/get_order_list_card_list with status code 403", "success": false}, {"endpoint": "get_one_order_sn", "url": "https://seller.shopee.com.my/api/v3/order/get_one_order", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/get_one_order with status code 403", "success": false}, {"endpoint": "get_one_order_id", "url": "https://seller.shopee.com.my/api/v3/order/get_one_order", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/get_one_order with status code 403", "success": false}, {"endpoint": "order_details_alternative", "url": "https://seller.shopee.com.my/api/v3/order/get_order_detail", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/get_order_detail with status code 403", "success": false}, {"endpoint": "get_one_order_combined", "url": "https://seller.shopee.com.my/api/v3/order/get_one_order", "error": "Authentication failed for https://seller.shopee.com.my/api/v3/order/get_one_order with status code 403", "success": false}]